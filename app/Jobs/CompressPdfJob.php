<?php

namespace App\Jobs;

use App\Services\ILovePdfService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Storage;

class CompressPdfJob implements ShouldQueue
{
    use Queueable;

    public int $timeout = 300; // 5 minutes

    public function __construct(public string $filePath) {}

    public function handle(): void
    {
        if (!Storage::exists($this->filePath)) {
            throw new \Exception("File not found: {$this->filePath}");
        }

        if (Storage::mimeType($this->filePath) !== 'application/pdf') {
            throw new \Exception('File is not a PDF');
        }

        $compressedContent = ILovePdfService::compressPdf($this->filePath);

        postHog([
            'event' => 'PDF compressed',
        ]);

        Storage::put($this->filePath, $compressedContent);
    }
}

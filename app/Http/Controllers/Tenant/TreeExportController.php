<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Services\CloudflarePdfService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TreeExportController extends Controller
{
    public function show(Request $request)
    {
        if (tenant()) {
            return inertia('Tenant/Branch/Export');
        }

        $data = cache()->get("tree_export_token:{$request->query('token')}");

        abort_if(!$data, 404);

        auth()->login(Tenant::findOrFail($data['tenant_id'])->owner);

        return inertia('Tenant/Branch/Export');
    }

    public function exportPdf()
    {
        $exportUrl = route('tree.export.show', [
            'branch' => tenant()->getMainBranch()->id,
            'token' => $this->generateExportToken(),
        ]);

        postHog(['event' => 'Tree PDF Export']);

        return CloudflarePdfService::convertUrlToPdf(
            $exportUrl,
            'tree_export_' . tenant()->getMainBranch()->id . '_' . now()->format('Y_m_d_H_i_s')
        );
    }

    private function generateExportToken(): string
    {
        $token = Str::random(32);

        cache()->put("tree_export_token:{$token}", ['tenant_id' => tenant()->id], now()->addMinutes(5));

        return $token;
    }
}

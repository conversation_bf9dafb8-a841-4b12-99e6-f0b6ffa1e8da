<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\DocumentResource;
use App\Jobs\CompressPdfJob;
use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DocumentController extends Controller
{
    public function index()
    {
        return inertia('Tenant/Document/Index', [
            'documents' => DocumentResource::collection(Document::latest()->paginate(15)),
        ]);
    }

    public function create()
    {
        return inertia('Tenant/Document/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'file' => ['nullable', 'file', 'max:50240', 'mimes:pdf,png,jpg,jpeg'], // 50MB max
        ]);

        $file = $request->file('file');

        $document = Document::create([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'file_path' => $file->store('documents'),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'original_filename' => $file->getClientOriginalName(),
        ]);

        if ($document->isPdf()) {
            CompressPdfJob::dispatch($document->file_path);
        }

        postHog([
            'event' => 'Document created',
            [
                'document_id' => $document->id,
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
            ],
        ]);

        return redirect()->route('documents.index')->with('success', 'تم رفع المستند بنجاح');
    }

    public function edit(Document $document)
    {
        return inertia('Tenant/Document/Edit', [
            'document' => new DocumentResource($document),
        ]);
    }

    public function update(Request $request, Document $document)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'file' => ['nullable', 'file', 'max:50240', 'mimes:pdf,png,jpg,jpeg'], // 50MB max
        ]);

        if ($request->hasFile('file')) {
            $file = $request->file('file');

            Storage::delete($document->file_path);

            $data = [
                'title' => $request->input('title'),
                'description' => $request->input('description'),
                'file_path' => $file->store('documents'),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'original_filename' => $file->getClientOriginalName(),
            ];

            $document->update($data);

            if ($file->getMimeType() === 'application/pdf') {
                ray('dispatch');

                CompressPdfJob::dispatch($document->file_path);
            }
        } else {
            $document->update([
                'title' => $request->input('title'),
                'description' => $request->input('description'),
            ]);
        }

        postHog([
            'event' => 'Document updated',
            [
                'document_id' => $document->id,
            ],
        ]);

        return redirect()->route('documents.index')->with('success', 'تم تعديل المستند بنجاح');
    }

    public function destroy(Document $document)
    {
        // Delete file
        if ($document->file_path) {
            Storage::delete($document->file_path);
        }

        $document->delete();

        postHog([
            'event' => 'Document deleted',
            [
                'document_id' => $document->id,
            ],
        ]);

        return redirect()->route('documents.index')->with('success', 'تم حذف المستند بنجاح');
    }
}

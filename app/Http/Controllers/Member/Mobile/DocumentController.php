<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use App\Http\Resources\DocumentResource;
use App\Models\Document;
use function json;
use function request;

class DocumentController extends Controller
{
    public function index()
    {
        return json(data: DocumentResource::collection(Document::query()->latest()->paginate(request('per_page', 15))));
    }
}

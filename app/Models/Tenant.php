<?php

namespace App\Models;

use App\Jobs\ProcessNodeMostCommonFullNamesJob;
use App\Jobs\ProcessNumberOfNodeForEachGenerationJob;
use App\Traits\TenantRelations;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends Model
{
    use HasFactory;
    use TenantRelations;

    protected $appends = ['tree_url', 'did_reach_max_number_of_nodes', 'all_nodes_count'];

    protected function casts(): array
    {
        return [
            'created_at' => 'date:Y-m-d',
        ];
    }

    protected function name(): Attribute
    {
        return Attribute::get(fn() => $this->getMainBranch()->name);
    }

    public function treeUrl(): Attribute
    {
        return Attribute::get(function () {
            $urlName = $this->getSetting(Setting::URL);
            $appName = config('app.domain');

            return $urlName === 'tree.alrashoodi.sa' ? 'https://tree.alrashoodi.sa' : "https://$urlName.$appName";
        });
    }

    public function scopeDemo(Builder $query): Builder
    {
        return $query->whereHas('users', fn($query) => $query->where('mobile', config('app.demo_mobile')));
    }

    public function scopeSetting(Builder $query, string $key, $value)
    {
        return $query->whereHas('settings', fn($query) => $query->keyValue($key, $value));
    }

    protected function maxNodesNumber(): Attribute
    {
        return Attribute::get(fn($value) => $value ?? 200);
    }

    protected function allNodesCount(): Attribute
    {
        return Attribute::get(fn() => $this->rememberCache('node_count', fn() => $this->nodes()->count()));
    }

    public function didReachMaxNumberOfNodes(): Attribute
    {
        return Attribute::get(fn() => $this->all_nodes_count >= $this->max_nodes_number);
    }

    public function rememberCache($key, callable $callback)
    {
        return cache()->remember($this->id . $key, now()->addDay(), $callback);
    }

    public function forgetCache($key): bool
    {
        return cache()->forget($this->id . $key);
    }

    public function getMainBranch(): Branch
    {
        return $this->rememberCache('main_branch', fn() => $this->branches()->first());
    }

    public function delete()
    {
        DB::transaction(function () {
            $this->branches->each(fn($branch) => $branch->nodes()->delete());
            $this->branches()->delete();
            $this->settings()->delete();
            $this->members()->delete();
            $this->referees()->delete();
            $this->users()->delete();
            parent::delete();
        });
    }

    public function mobileAppEnabled(): bool
    {
        return $this->getSetting(Setting::MOBILE_APP_ENABLED);
    }

    public function allowGuests()
    {
        return $this->getSetting(Setting::ALLOW_GUESTS);
    }

    public function getSetting(string $key): mixed
    {
        return $this->settings->firstWhere('key', $key)->value;
    }

    public function nodeAdditions(): HasMany
    {
        return $this->hasMany(NodeAddition::class, 'tenant_id');
    }

    public function nodeChanges(): HasMany
    {
        return $this->hasMany(NodeChange::class, 'tenant_id');
    }

    public function updateStats(): void
    {
        ProcessNodeMostCommonFullNamesJob::dispatch($this);
        ProcessNumberOfNodeForEachGenerationJob::dispatch($this);
    }
}

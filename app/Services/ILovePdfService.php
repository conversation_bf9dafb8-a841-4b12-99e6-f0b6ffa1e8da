<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Ilovepdf\Ilovepdf;

class ILovePdfService
{
    public static function compressPdf(string $filePath): string
    {
        if (!Storage::exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $tempName = Str::random(10);

        $tempFilePath = Storage::disk('local')->path("temp/$tempName.pdf");

        Storage::disk('local')->put("temp/$tempName.pdf", Storage::get($filePath));

        try {
            $compressTask = (new Ilovepdf(
                config('services.ilove_pdf.public_key'),
                config('services.ilove_pdf.secret_key')
            ))->newTask('compress');

            $compressTask->addFile($tempFilePath);
            $compressTask->setCompressionLevel('recommended');
            $compressTask->execute();

            $compressTask->download(Storage::disk('local')->path('temp'));

            return file_get_contents($tempFilePath);
        } finally {
            // Clean up temp file
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }
        }
    }
}

<?php

namespace App\Services;

use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class CloudflarePdfService
{
    public static function convertUrlToPdf(string $url, string $filename = 'export'): Response
    {
        $accountId = config('services.cloudflare.account_id');
        $apiToken = config('services.cloudflare.api_token');

        if (!$accountId || !$apiToken) {
            throw new Exception('Cloudflare credentials not configured');
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$apiToken}",
            'Content-Type' => 'application/json',
        ])
            ->timeout(180)
            ->post("https://api.cloudflare.com/client/v4/accounts/{$accountId}/browser-rendering/pdf", [
                'url' => $url,
                'gotoOptions' => [
                    'waitForSelector' => '#tree-finished',
                    'viewport' => [
                        'width' => 1920,
                        'height' => 1080,
                    ],
                ],
            ])
            ->throw();

        $pdfContent = $response->body();

        return response($pdfContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => "attachment; filename=\"{$filename}.pdf\"",
            'Content-Length' => strlen($pdfContent),
        ]);
    }
}

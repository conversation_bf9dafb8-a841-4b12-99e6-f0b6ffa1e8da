<?php

use App\Http\Controllers\Auth\Mobile\CurrentUserController;
use App\Http\Controllers\Member\Mobile\DocumentController;
use App\Http\Controllers\Member\Mobile\ExpoPushTokenController;
use App\Http\Controllers\Member\Mobile\LineController;
use App\Http\Controllers\Member\Mobile\NodeController;
use App\Http\Controllers\Member\Mobile\NodeFilterController;
use App\Http\Controllers\Member\Mobile\NotificationController;
use App\Http\Controllers\Member\Mobile\PostController;
use App\Http\Controllers\Member\Mobile\QuickStatisticController;
use App\Http\Controllers\Member\Mobile\SettingController;
use App\Http\Controllers\Member\Mobile\StatisticController;
use App\Http\Controllers\Member\Mobile\TreeChartController;
use App\Http\Controllers\Member\NodeAdditionController;
use App\Http\Controllers\Member\NodeChangeController;
use App\Http\Middleware\MembersOnlyMiddleware;

Route::get('posts', [PostController::class, 'index']);

Route::get('documents', [DocumentController::class, 'index']);

Route::get('quick-stats', QuickStatisticController::class);
Route::get('stats', StatisticController::class);

Route::get('tree-chart', TreeChartController::class);

Route::get('nodes', [NodeController::class, 'index']);
Route::get('nodes/filter', [NodeFilterController::class, 'index']);
Route::get('nodes/{node}', [NodeController::class, 'show']);

Route::get('lines', [LineController::class, 'index']);

Route::get('settings', [SettingController::class, 'index']);

Route::get('notifications', [NotificationController::class, 'index']);
Route::put('notifications/{notification}/read', [NotificationController::class, 'markAsRead']);
Route::post('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
Route::get('notifications/unread-count', [NotificationController::class, 'unreadNotificationCount']);

Route::middleware(MembersOnlyMiddleware::class)->group(function () {
    Route::get('node-additions', [NodeAdditionController::class, 'index']);
    Route::post('node-additions', [NodeAdditionController::class, 'store']);

    Route::post('node-changes/{node}', [NodeChangeController::class, 'store']);
    Route::get('node-changes', [NodeChangeController::class, 'index']);
    Route::post('node-changes/{node}', [NodeChangeController::class, 'store']);
});

Route::get('user/{token?}', CurrentUserController::class);

Route::post('expo-push-token', ExpoPushTokenController::class);

<?php

use App\Http\Controllers\PaymentController;
use App\Http\Controllers\Tenant\AppSettingController;
use App\Http\Controllers\Tenant\Branch\BranchController;
use App\Http\Controllers\Tenant\Branch\BulkNodeController;
use App\Http\Controllers\Tenant\Branch\ChangeRootController;
use App\Http\Controllers\Tenant\Branch\CityController;
use App\Http\Controllers\Tenant\Branch\CountryController;
use App\Http\Controllers\Tenant\Branch\DistrictController;
use App\Http\Controllers\Tenant\Branch\NodeController;
use App\Http\Controllers\Tenant\Branch\NodeFilterController;
use App\Http\Controllers\Tenant\Branch\NodeLabelsController;
use App\Http\Controllers\Tenant\Branch\NodeTransferController;
use App\Http\Controllers\Tenant\Branch\NodeUserController;
use App\Http\Controllers\Tenant\Branch\ResetAllNodesStyleController;
use App\Http\Controllers\Tenant\Branch\UpdateAddedToPaperAtController;
use App\Http\Controllers\Tenant\Branch\UpdateAllAddedToPaperAtController;
use App\Http\Controllers\Tenant\Branch\UpdateAllFullNameController;
use App\Http\Controllers\Tenant\DeleteFileSettingController;
use App\Http\Controllers\Tenant\DocumentController;
use App\Http\Controllers\Tenant\GeneralSettingController;
use App\Http\Controllers\Tenant\LineController;
use App\Http\Controllers\Tenant\NodeAddition\NodeAdditionApproveController;
use App\Http\Controllers\Tenant\NodeAddition\NodeAdditionController;
use App\Http\Controllers\Tenant\NodeAddition\NodeAdditionExportController;
use App\Http\Controllers\Tenant\NodeAddition\NodeAdditionRejectController;
use App\Http\Controllers\Tenant\NodeBulkStyleController;
use App\Http\Controllers\Tenant\NodeChange\NodeChangeApproveController;
use App\Http\Controllers\Tenant\NodeChange\NodeChangeController;
use App\Http\Controllers\Tenant\NodeChange\NodeChangeExportController;
use App\Http\Controllers\Tenant\NodeChange\NodeChangeRejectController;
use App\Http\Controllers\Tenant\NodeExportController;
use App\Http\Controllers\Tenant\NodeListController;
use App\Http\Controllers\Tenant\NodeRelationshipController;
use App\Http\Controllers\Tenant\NodeRelativeController;
use App\Http\Controllers\Tenant\NodeStyleController;
use App\Http\Controllers\Tenant\OnboardingController;
use App\Http\Controllers\Tenant\PostController;
use App\Http\Controllers\Tenant\SetNodeInitialStyleController;
use App\Http\Controllers\Tenant\StatisticController;
use App\Http\Controllers\Tenant\TreeDistributionController;
use App\Http\Controllers\Tenant\TreeExportController;
use App\Http\Controllers\Tenant\UserController;

Route::get('/branches/show/{node?}', [BranchController::class, 'show'])->name('branches.show');

Route::prefix('relationships')
    ->name('relationships.')
    ->controller(NodeRelationshipController::class)
    ->group(function () {
        Route::get('{node}', 'index')->name('index');
    });

Route::prefix('posts')
    ->name('posts.')
    ->controller(PostController::class)
    ->group(function () {
        Route::get('/', 'index')->name('index');
        Route::post('/', 'store')->name('store');
        Route::get('create', 'create')->name('create');
        Route::get('{post}/edit', 'edit')->name('edit');
        Route::post('{post}', 'update')->name('update');
        Route::delete('{post}', 'destroy')->name('destroy');
    });

Route::prefix('documents')
    ->name('documents.')
    ->controller(DocumentController::class)
    ->group(function () {
        Route::get('/', 'index')->name('index');
        Route::post('/', 'store')->name('store');
        Route::get('create', 'create')->name('create');
        Route::get('{document}/edit', 'edit')->name('edit');
        Route::post('{document}', 'update')->name('update');
        Route::delete('{document}', 'destroy')->name('destroy');
    });

Route::prefix('relatives')
    ->name('relatives.')
    ->controller(NodeRelativeController::class)
    ->group(function () {
        Route::get('{node}', 'index')->name('index');
    });

Route::get('nodes/download', [NodeExportController::class, 'index'])->name('branches.nodes.download');

// Tree export routes
Route::get('tree/export', [TreeExportController::class, 'show'])
    ->name('tree.export.show')
    ->withoutMiddleware(['auth:sanctum', 'inside-tenant']);
Route::get('tree/export-pdf', [TreeExportController::class, 'exportPdf'])->name('tree.export.pdf');

Route::get('{node}/children', [NodeController::class, 'children'])->name('branches.nodes.children');
Route::get('filter', [NodeFilterController::class, 'index'])
    ->prefix('nodes')
    ->name('nodes.filter.index');
Route::get('labels', [NodeLabelsController::class, 'index'])
    ->prefix('nodes')
    ->name('nodes.labels.index');

Route::post('nodes/{node}/transfer', [NodeTransferController::class, 'update'])->name('nodes.transfer');

Route::post('nodes/{node}/add-to-paper', [UpdateAddedToPaperAtController::class, 'update'])->name(
    'branches.nodes.add-to-paper'
);

Route::post('nodes/add-to-paper', [UpdateAllAddedToPaperAtController::class, 'update'])->name(
    'branches.nodes.add-all-to-paper'
);

Route::post('nodes/change-root', ChangeRootController::class)->name('branches.nodes.change-root');
Route::post('nodes/change-root', ChangeRootController::class)->name('branches.nodes.change-root');
Route::post('nodes/update-full-name', UpdateAllFullNameController::class)->name('branches.nodes.update-full-name');

Route::put('nodes/reset-style', ResetAllNodesStyleController::class)->name('branches.nodes.reset-style');

Route::get('lines', [LineController::class, 'index'])->name('branches.lines.index');
Route::put('lines/{line}', [LineController::class, 'update'])->name('branches.lines.update');

Route::post('nodes/bulk-store', [BulkNodeController::class, 'store'])->name('nodes.bulk-store');

Route::put('nodes/{node}/set-initial-style', SetNodeInitialStyleController::class)->name(
    'branches.nodes.set-initial-style'
);

Route::put('nodes/{node}/style', NodeStyleController::class)->name('branches.nodes.style');

Route::put('nodes/bulk-style', NodeBulkStyleController::class)->name('branches.nodes.bulk-style');

Route::post('nodes/{node}', [NodeController::class, 'update'])->name('nodes.update');

Route::apiResource('nodes', NodeController::class)->except(['update']);

Route::prefix('users')
    ->name('users.')
    ->group(function () {
        Route::get('api', [UserController::class, 'indexApi'])->name('index-api');
        Route::post('/store', [UserController::class, 'store'])->name('store');
        Route::put('/update/{user}', [UserController::class, 'update'])->name('update');
        Route::put('attach-users-to-node/{node}', [NodeUserController::class, 'update'])->name('attach-users-to-node');
    });

Route::resource('users', UserController::class)->only(['index', 'create', 'edit', 'destroy']);

Route::prefix('settings')
    ->name('settings.')
    ->group(function () {
        Route::get('general', [GeneralSettingController::class, 'index'])->name('general.index');
        Route::post('general', [GeneralSettingController::class, 'update'])->name('general.update');

        Route::get('app', [AppSettingController::class, 'index'])->name('app.index');
        Route::post('app', [AppSettingController::class, 'update'])->name('app.update');

        Route::delete('{fileName}', DeleteFileSettingController::class)->name('destroy');
    });

Route::prefix('node-additions')
    ->name('node-additions.')
    ->group(function () {
        Route::get('/', [NodeAdditionController::class, 'index'])->name('index');
        Route::get('export', NodeAdditionExportController::class)->name('export');
        Route::post('{nodeAddition}/approve', NodeAdditionApproveController::class)->name('approve');
        Route::post('{nodeAddition}/reject', NodeAdditionRejectController::class)->name('reject');
    });

Route::prefix('node-changes')
    ->name('node-changes.')
    ->group(function () {
        Route::get('/', [NodeChangeController::class, 'index'])->name('index');
        Route::get('export', NodeChangeExportController::class)->name('export');
        Route::post('{nodeChange}/approve', NodeChangeApproveController::class)->name('approve');
        Route::post('{nodeChange}/reject', NodeChangeRejectController::class)->name('reject');
    });

Route::prefix('distribution')
    ->name('distribution.')
    ->group(function () {
        Route::get('/', [TreeDistributionController::class, 'index'])->name('index');
    });

Route::resource('statistics', StatisticController::class)->only('index');

Route::get('node-list', [NodeListController::class, 'index'])->name('node-list.index');

Route::get('onboarding', OnboardingController::class)->name('onboarding');

Route::get('countries', [CountryController::class, 'index'])->name('countries.index');
Route::get('cities', [CityController::class, 'index'])->name('cities.index');
Route::get('districts', [DistrictController::class, 'index'])->name('districts.index');

Route::get('payments/verify', [PaymentController::class, 'verify'])->name('payments.verify');

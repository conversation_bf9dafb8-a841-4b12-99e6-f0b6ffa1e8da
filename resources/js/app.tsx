import { Toaster } from '@/components/ui/sonner';
import { createInertiaApp, router } from '@inertiajs/react';
import { DirectionProvider } from '@radix-ui/react-direction';
import * as Sentry from '@sentry/react';
import { QueryClient } from '@tanstack/react-query';
import { PersistedClient, Persister, PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { ReactFlowProvider } from '@xyflow/react';
import { del, get, set } from 'idb-keyval';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { NuqsAdapter } from 'nuqs/adapters/react';
import posthog from 'posthog-js';
import { createRoot } from 'react-dom/client';
import { scan } from 'react-scan'; // must be imported before React and React DOM
import { toast } from 'sonner';
import '../css/app.css';

if (import.meta.env.DEV) {
  scan({ enabled: false });
}

const appName = import.meta.env.VITE_APP_NAME || 'أوراق';

if (import.meta.env.PROD) {
  Sentry.init({
    dsn: 'https://<EMAIL>/5887404',
    integrations: [Sentry.browserTracingIntegration()],
    tracesSampleRate: 0.5,
  });

  posthog.init('phc_CVPSRzgzGDrJZbYhLaMHm94BNxaQWdxBOiPe1yUr4HN', {
    api_host: 'https://us.i.posthog.com',
    capture_pageview: false,
  });
}

const idbValidKey: IDBValidKey = 'reactQuery';

export const persister: Persister = {
  persistClient: async (client: PersistedClient) => {
    await set(idbValidKey, client);
  },
  restoreClient: async () => {
    return await get<PersistedClient>(idbValidKey);
  },
  removeClient: async () => {
    await del(idbValidKey);
  },
};

const queryClient = new QueryClient();

createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: (name) => resolvePageComponent(`./Pages/${name}.tsx`, import.meta.glob('./Pages/**/*.tsx')),
  setup({ el, App, props }) {
    const root = createRoot(el);

    root.render(
      <PersistQueryClientProvider client={queryClient} persistOptions={{ persister }}>
        <NuqsAdapter>
          <DirectionProvider dir="rtl">
            <ReactFlowProvider>
              <App {...props} />
              <Toaster position="top-right" />
            </ReactFlowProvider>
          </DirectionProvider>
        </NuqsAdapter>
      </PersistQueryClientProvider>,
    );
  },
  progress: {
    delay: 0,
    color: '#34d399',
    showSpinner: false,
  },
});

router.on('success', (event) => {
  if (event.detail.page.props.success) {
    toast.success(event.detail.page.props.success as string);
  }
});

router.on('navigate', (event) => {
  const firstError = Object.values(event.detail.page.props.errors ?? {})[0];

  if (firstError) {
    toast.error(firstError);
  }

  gtag('config', 'G-DZGCE7C5TF', {
    send_page_view: false,
  });

  gtag('event', 'page_view', {
    page_location: event.detail.page.url,
  });

  posthog.capture('$pageview');
});

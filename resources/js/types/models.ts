// Base Laravel Model Interface
export interface BaseModel {
  id: number;
  created_at: string;
  updated_at: string;
}

// Base Tenant Model Interface
export interface BaseTenantModel extends BaseModel {
  tenant_id: number;
}

// Base Authenticatable Model Interface
export interface BaseAuthenticatable extends BaseModel {
  email?: string;
  email_verified_at?: string;
  remember_token?: string;
}

// Enum Types
export type Gender = 'male' | 'female';
export type LifeStatus = 'alive' | 'dead' | 'unknown';
export type RequestStatus = 'pending' | 'approved' | 'rejected';
export type IdentifierType = 'email' | 'mobile';
export type TreeType = 'all' | 'normal' | 'vertical';
export type RelationshipStatus = 'marriage' | 'divorce' | 'widow';

// Location Models
export interface Country extends BaseModel {
  name: string;
  name_ar: string;
}

export interface City extends BaseModel {
  name: string;
  name_ar: string;
  country_id: number;
  districts?: District[];
}

export interface District extends BaseModel {
  name: string;
  name_ar: string;
  city_id: number;
  city?: City;
}

// Admin Model
export interface Admin extends BaseAuthenticatable {
  name: string;
  password?: string;
}

// Tenant Model
export interface Tenant extends BaseModel {
  name: string;
  url: string;
  created_at: string; // Casted to Y-m-d format
  tree_url: string; // Appended
  did_reach_max_number_of_nodes: boolean; // Appended
  all_nodes_count: number; // Appended
  max_nodes: number;
  is_eligible_for_discount: boolean;
  nodeAdditions?: NodeAddition[];
  nodeChanges?: NodeChange[];
}

// Branch Model
export interface Branch extends BaseTenantModel {
  name: string;
  description?: string;
  nodes?: NodeModel[];
  users?: User[];
}

// User Models
export interface User extends BaseAuthenticatable, BaseTenantModel {
  name: string;
  mobile?: string;
  password?: string;
  google_id?: string;
  google_avatar?: string;
  expo_token?: string;
  resource_name: string; // Appended - always 'user'
  is_demo: boolean; // Appended
  tenant?: Tenant;
  messages?: SMSMessage[];
  branches?: Branch[];
  nodes?: NodeModel[];
}

export interface Member extends BaseAuthenticatable, BaseTenantModel {
  name: string;
  mobile?: string;
  password?: string;
  expo_token?: string;
  resource_name: string; // Appended - always 'member'
  node_id?: number;
  node?: NodeModel;
  nodeAddition?: NodeAddition;
  nodeChanges?: NodeChange[];
}

export interface Guest extends BaseTenantModel {
  name: string;
  resource_name: string; // Appended - always 'guest'
  expo_token?: string;
}

// Core NodeModel Model
export interface NodeModel extends BaseTenantModel {
  id: number;
  name: string;
  full_name?: string;
  nickname?: string;
  about?: string;
  mobile?: string;
  email?: string;
  gender: Gender;
  life_status: LifeStatus;
  life_status_ar: string; // Appended
  birth_date?: string; // Casted to Y-m-d format
  death_date?: string; // Casted to Y-m-d format
  added_to_paper_at?: string; // Casted to Y-m-d format
  photo?: string;
  photo_url?: string; // Appended
  bg_color?: string;
  size?: number;
  label?: string;
  order?: number;
  parent_id: number | null;
  branch_id?: number;
  country_id?: number;
  city_id?: number;
  district_id?: number;
  other_parent_relationship_id?: number;
  is_root: boolean;
  style: {
    x: number;
    y: number;
    scale?: number;
    rotation?: number;
  }; // JSON field

  // Relationships
  branch?: Branch;
  parent: NodeModel | null;
  children?: NodeModel[];
  ancestors?: NodeModel[];
  descendants?: NodeModel[];
  siblings?: NodeModel[];
  users?: User[];
  visibility?: NodeVisibility;
  wives?: Relationship[];
  husbands?: Relationship[];
  country?: Country;
  city?: City;
  district?: District;
  member?: Member;
  node_additions?: NodeAddition[];
  node_changes?: NodeChange[];
  other_parent_relationship?: Relationship;
}

// Relationship Model
export interface Relationship extends BaseTenantModel {
  husband_id: number;
  wife_id: number;
  name?: string;
  family_name?: string;
  status: RelationshipStatus;
  other_parent_relationship_id?: number;
  is_outside_family: boolean; // Appended

  // Relationships
  husband?: NodeModel | null;
  wife?: NodeModel | null;
  other_parent_relationship?: Relationship;

  node: NodeModel | null;
}

// NodeModel Request Models
export interface NodeAddition extends BaseTenantModel {
  parent_id: number;
  member_id: number;
  node_attributes: Record<string, unknown>; // Collection cast
  status: RequestStatus;
  status_ar: string; // Appended

  // Relationships
  parent?: NodeModel;
  member?: Member;
}

export interface NodeChange extends BaseTenantModel {
  node_id: number;
  member_id: number;
  new_attributes: Record<string, unknown>; // Array cast
  old_attributes: Record<string, unknown>; // Array cast
  status: RequestStatus;
  status_ar: string; // Appended

  // Relationships
  node?: NodeModel;
  member?: Member;
}

// NodeModel Visibility Model
export interface NodeVisibility extends BaseTenantModel {
  node_id: number;
  hidden_attributes: string[]; // Collection cast

  // Constants for reference
  readonly NODE_ATTRIBUTES: readonly string[];
  readonly DEFAULT_VISIBLE_ATTRIBUTES: readonly string[];
}

// Line Model (for tree visualization)
export interface Line extends BaseTenantModel {
  from_node_id: number;
  to_node_id: number;
  points: number[][]; // Array cast

  // Relationships
  fromNode?: NodeModel;
  toNode?: NodeModel;
}

// Post Model
export interface Post extends BaseTenantModel {
  title: string;
  content?: string;
  image?: string;
  image_url?: string; // Appended
  published_at?: string; // Datetime cast
  start_at?: string; // Datetime cast
  end_at?: string; // Datetime cast
  links?: { label: string; value: string }[]; // Array cast
}

// Package Model
export interface Package extends BaseModel {
  name: string;
  nodes: number;
  price: number;
  savings?: number;
  is_discounted: boolean;
  original_price?: number;
  is_active: boolean;

  // Appended attributes
  formatted_price: string;
  formatted_nodes: string;
  formatted_savings?: string;
  price_per_node: string;
  discount_percentage?: number;
}

// Frontend Package Type (for Inertia props)
export interface FrontendPackage {
  id: string;
  nodes: number;
  nodesFormatted: string;
  price: number;
  priceFormatted: string;
  pricePerNode: string;
  savings?: number;
  savingsFormatted?: string;
  isDiscounted?: boolean;
  originalPrice?: number;
  originalPriceFormatted?: string;
  discountPercentage?: number;
}

// Invoice Model
export interface Invoice extends BaseTenantModel {
  package_id: number;
  amount: number;
  currency: string;
  moyasar_payment_id: string;
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  metadata?: Record<string, unknown>;
  paid_at?: string;

  // Relationships
  package?: Package;
}

// Document Model
export interface Document extends BaseTenantModel {
  title: string;
  description?: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  original_filename: string;
  type: 'pdf' | 'image';
  // Appended attributes
  formatted_file_size: string;
  file_extension: string;
  file_url: string;
}

// Setting Model
export interface Setting extends BaseTenantModel {
  key: string;
  value?: string | number | boolean;

  // Setting key constants for reference
  readonly FAMILY_NAME: 'family_name';
  readonly URL: 'url';
  readonly PHONE: 'phone';
  readonly EMAIL: 'email';
  readonly FACEBOOK: 'facebook';
  readonly TWITTER: 'twitter';
  readonly INSTAGRAM: 'instagram';
  readonly YOUTUBE: 'youtube';
  readonly LINKEDIN: 'linkedin';
  readonly WHATSAPP: 'whatsapp';
  readonly TELEGRAM: 'telegram';
  readonly SNAPCHAT: 'snapchat';
  readonly TIKTOK: 'tiktok';
  readonly WEBSITE: 'website';
  readonly DESCRIPTION: 'description';
  readonly LOGO: 'logo';
  readonly FAVICON: 'favicon';
  readonly PASSWORD: 'password';
  readonly ENABLE_PASSWORD: 'enable_password';
}

// Statistics Model
export interface Stat extends BaseTenantModel {
  most_common_full_names?: Record<string, number>; // Collection cast
  number_of_nodes_in_each_generation?: Record<string, number>; // Collection cast
}

// Tag Model (extends Spatie Tag)
export interface Tag extends BaseTenantModel {
  name: string;
  slug: string;
  type?: string;
  order_column?: number;
}

// SMS Message Model
export interface SMSMessage extends BaseModel {
  mobile: string;
  message: string;
  sent_at?: string; // Datetime cast
  messageable_id?: number;
  messageable_type?: string;
}

// OTP Model
export interface OneTimePassword extends BaseModel {
  identifier: string;
  identifier_type: IdentifierType;
  token: string;
  expires_at: string;
}

type Links = Array<{
  url: string;
  label: string;
  active: boolean;
}>;

export interface PaginatedResource<T> {
  data: T[];
  meta: {
    current_page: number;
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url?: string;
    path: string;
    per_page: number;
    prev_page_url?: string;
    to: number;
    total: number;
    links: Links;
  };
  links: {
    first: string;
    last: string;
    prev?: string;
    next?: string;
  };
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

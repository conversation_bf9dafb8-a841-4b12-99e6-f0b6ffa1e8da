import AppLayout from '@/Layouts/AppLayout';
import Pagination, { PaginationData } from '@/components/Pagination';
import { TreeExportButton } from '@/components/tree/TreeExportButton';
import Card from '@/components/ui/Card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useNodeModalStore } from '@/store/node-modal';
import { NodeModel } from '@/types/models';
import { debounceReload } from '@/utils/helpers';
import { clsx } from 'clsx';
import { useQueryState } from 'nuqs';
import { useEffect } from 'react';

interface Props {
  nodes: PaginationData<NodeModel>;
}

export default function Index({ nodes }: Props) {
  const [name, setName] = useQueryState('name');

  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  useEffect(() => {
    debounceReload({ name });
  }, [name]);

  return (
    <AppLayout
      header={
        <>
          <div>قائمة الأفراد</div>
          <div className="mt-1 text-xs font-normal text-gray-500">
            هنا يمكنك تصفح جميع أفراد العائلة والبحث عنهم بسهولة
          </div>
        </>
      }
    >
      <div className="flex flex-col gap-4">
        <Card>
          <div className="mb-6 grid gap-4 md:grid-cols-3">
            <div>
              <Label>الاسم</Label>
              <Input
                value={name || ''}
                onChange={(e) => setName(e.target.value || null)}
                placeholder="ابحث بالاسم..."
              />
            </div>
          </div>
        </Card>
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الرقم</TableHead>
                <TableHead>الاسم</TableHead>
                <TableHead>الجنس</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>الموقع</TableHead>
                <TableHead>رقم الجوال</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>تاريخ الإضافة</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {nodes.data.length > 0 ? (
                nodes.data.map((node) => (
                  <TableRow key={node.id}>
                    <TableCell>{node.id}</TableCell>
                    <TableCell className="max-w-64 truncate font-semibold">{node.full_name}</TableCell>
                    <TableCell>
                      <span
                        className={clsx(
                          'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                          {
                            'bg-blue-50 text-blue-700 ring-blue-600/20': node.gender === 'male',
                            'bg-pink-50 text-pink-700 ring-pink-600/20': node.gender === 'female',
                          },
                        )}
                      >
                        {node.gender === 'male' ? 'ذكر' : 'أنثى'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                        className={clsx(
                          'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                          {
                            'bg-green-50 text-green-700 ring-green-600/20': node.life_status === 'alive',
                            'bg-red-50 text-red-700 ring-red-600/20': node.life_status === 'dead',
                            'bg-gray-50 text-gray-700 ring-gray-600/20': node.life_status === 'unknown',
                          },
                        )}
                      >
                        {node.life_status_ar}
                      </span>
                    </TableCell>
                    <TableCell>
                      {node.country || node.city || node.district ? (
                        <div className="text-xs">
                          {[node.country?.name, node.city?.name, node.district?.name].filter(Boolean).join(' - ')}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-400">-</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {node.mobile ? (
                        <div className="text-xs">{node.mobile}</div>
                      ) : (
                        <div className="text-xs text-gray-400">-</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {node.email ? (
                        <div className="text-xs">{node.email}</div>
                      ) : (
                        <div className="text-xs text-gray-400">-</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-xs">{new Date(node.created_at).toLocaleDateString()}</div>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" onClick={() => setNodeModal('NODE_INFO', node)}>
                        عرض
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    لا يوجد أفراد مطابقة للبحث
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Pagination data={nodes} />
        </Card>
      </div>
      <TreeExportButton />
    </AppLayout>
  );
}

import AppLayout from '@/Layouts/AppLayout';
import Pagination from '@/components/Pagination';
import Card from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Document, PaginatedResource } from '@/types/models';
import { DownloadIcon, EditIcon, RemoveIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { Link, router } from '@inertiajs/react';
import dayjs from 'dayjs';

interface Props {
  documents: PaginatedResource<Document>;
}

export default function Index({ documents }: Props) {
  const formatDateTime = (date: string) => {
    return dayjs(date).format('YYYY/MM/DD HH:mm');
  };

  const getFileTypeColor = (mimeType: string) => {
    if (mimeType === 'application/pdf') return 'bg-red-100 text-red-800';
    if (mimeType.startsWith('image/')) return 'bg-green-100 text-green-800';
    if (mimeType.includes('word')) return 'bg-blue-100 text-blue-800';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getFileTypeName = (mimeType: string) => {
    if (mimeType === 'application/pdf') return 'PDF';
    if (mimeType.startsWith('image/')) return 'صورة';
    if (mimeType.includes('word')) return 'Word';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'Excel';
    return 'ملف';
  };

  const confirmDelete = (document: Document) => {
    if (confirm('هل أنت متأكد من حذف هذا المستند؟')) {
      router.delete(route('documents.destroy', document.id));
    }
  };

  return (
    <AppLayout
      header={<h2 className="text-xl leading-tight font-semibold text-gray-800">المستندات</h2>}
      topEnd={
        <Link href={route('documents.create')}>
          <Button>إضافة مستند</Button>
        </Link>
      }
    >
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>العنوان</TableHead>
              <TableHead>الوصف</TableHead>
              <TableHead>النوع</TableHead>
              <TableHead>الحجم</TableHead>
              <TableHead>تاريخ الإنشاء</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.data.map((document) => (
              <TableRow key={document.id}>
                <TableCell className="font-medium">{document.title}</TableCell>
                <TableCell>
                  <div className="max-w-xs truncate text-sm text-gray-600">{document.description || '-'}</div>
                </TableCell>
                <TableCell>
                  <Badge className={getFileTypeColor(document.mime_type)}>{getFileTypeName(document.mime_type)}</Badge>
                </TableCell>
                <TableCell className="text-sm">{document.formatted_file_size}</TableCell>
                <TableCell className="text-sm">{formatDateTime(document.created_at)}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <a href={document.file_url} target="_blank" rel="noopener noreferrer">
                        <HugeiconsIcon icon={DownloadIcon} size={16} />
                        عرض
                      </a>
                    </Button>
                    <Link href={route('documents.edit', document.id)}>
                      <Button variant="secondary" size="sm">
                        <HugeiconsIcon icon={EditIcon} size={16} />
                        تعديل
                      </Button>
                    </Link>
                    <Button variant="destructive" size="sm" onClick={() => confirmDelete(document)}>
                      <HugeiconsIcon icon={RemoveIcon} size={16} />
                      حذف
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <Pagination data={documents} />
      </Card>
    </AppLayout>
  );
}

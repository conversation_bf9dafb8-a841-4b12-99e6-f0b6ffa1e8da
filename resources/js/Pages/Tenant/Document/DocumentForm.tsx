import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import ValidationErrors from '@/components/ValidationErrors';
import { Document } from '@/types/models';
import { FileIcon, ViewIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { InertiaFormProps, useForm } from '@inertiajs/react';
import { ChangeEvent, FormEvent, useState } from 'react';

interface DocumentFormProps {
  document?: Document;
  submitButtonText?: string;
  onSubmit: (form: InertiaFormProps<DocumentForm>) => void;
}

export type DocumentForm = {
  title: Document['title'];
  description: Document['description'];
  file: File | null;
};

export default function DocumentForm({ document, submitButtonText = 'حفظ', onSubmit }: DocumentFormProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const form = useForm<DocumentForm>({
    title: document?.title ?? '',
    description: document?.description ?? '',
    file: null,
  });

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSubmit(form);
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    form.setData('file', file);
  };

  const formatFileSize = (bytes: number) => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  };

  console.log(document);
  return (
    <>
      <ValidationErrors />
      <form onSubmit={handleSubmit}>
        {/* Current file display for editing */}
        {document?.file_url && !selectedFile && (
          <div className="mb-6">
            {document.type === 'image' ? (
              // Show image preview
              <div className="rounded-lg border bg-gray-50 p-4">
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <HugeiconsIcon icon={FileIcon} size={20} className="text-gray-500" />
                    <div>
                      <p className="font-medium">{document.original_filename}</p>
                      <p className="text-sm text-gray-500">{document.formatted_file_size}</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <a href={document.file_url} target="_blank" rel="noopener noreferrer">
                      <HugeiconsIcon icon={ViewIcon} size={16} />
                      عرض
                    </a>
                  </Button>
                </div>
                <div className="overflow-hidden rounded-lg">
                  <img src={document.file_url} alt={document.title} className="max-h-64 w-full object-contain" />
                </div>
              </div>
            ) : (
              // Show file info with view button for PDFs and other files
              <div className="flex items-center justify-between rounded-lg border bg-gray-50 p-4">
                <div className="flex items-center gap-3">
                  <HugeiconsIcon icon={FileIcon} size={24} className="text-gray-500" />
                  <div>
                    <p className="font-medium">{document.original_filename}</p>
                    <p className="text-sm text-gray-500">{document.formatted_file_size}</p>
                  </div>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <a href={document.file_url} target="_blank" rel="noopener noreferrer">
                    <HugeiconsIcon icon={ViewIcon} size={16} />
                    عرض
                  </a>
                </Button>
              </div>
            )}
          </div>
        )}

        <div className="mt-4">
          <Input
            value={form.data.title}
            onChange={(e) => form.setData('title', e.target.value)}
            label="عنوان المستند"
            error={form.errors.title}
            required
          />
        </div>

        <div className="mt-4">
          <Textarea
            value={form.data.description}
            onChange={(e) => form.setData('description', e.target.value)}
            rows={4}
            label="الوصف"
            error={form.errors.description}
            placeholder="أدخل وصف المستند (اختياري)"
          />
        </div>

        <div className="mt-4">
          <Input
            label={document ? 'استبدال الملف (اختياري)' : 'الملف'}
            type="file"
            accept=".pdf,.jpg,.jpeg,.png"
            onChange={handleFileChange}
            error={form.errors.file}
            required={!document}
          />

          {/* Selected file info */}
          {selectedFile && (
            <div className="mt-2 flex items-center gap-3 rounded-lg border border-green-200 bg-green-50 p-3">
              <HugeiconsIcon icon={FileIcon} size={20} className="text-green-600" />
              <div className="flex-1">
                <p className="text-sm font-medium text-green-800">{selectedFile.name}</p>
                <p className="text-xs text-green-600">{formatFileSize(selectedFile.size)}</p>
              </div>
            </div>
          )}

          <p className="mt-1 text-xs text-gray-500">الملفات المدعومة: PDF, الصور</p>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <Button type="submit" disabled={form.processing}>
            {submitButtonText}
          </Button>
        </div>
      </form>
    </>
  );
}

import AppLayout from '@/Layouts/AppLayout';
import type { Document } from '@/types/models';
import DocumentForm from './DocumentForm';

interface Props {
  document: { data: Document };
}

export default function Edit({ document }: Props) {
  return (
    <AppLayout header={<h2 className="text-xl leading-tight font-semibold text-gray-800">تعديل مستند</h2>}>
      <DocumentForm
        document={document.data}
        submitButtonText="تحديث"
        onSubmit={(form) => form.post(route('documents.update', document.data.id))}
      />
    </AppLayout>
  );
}

import { Tree } from '@/components/tree/Tree';
import { Page, View } from '@react-pdf/renderer';

export default function Export() {
  return (
    <div className="min-h-screen bg-white">
      <Document>
        <Page size="A4" style={styles.page}>
          <View style={styles.section}>
            <div className="relative h-screen border !bg-white">
              <Tree editable={false} showSearch={false} hideNodesOnZoomOut={false} />
            </div>
          </View>
          <View style={styles.section}>
            <Text>Section #2</Text>
          </View>
        </Page>
      </Document>

      {/* Global styles for PDF export */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          /* Hide interactive elements for PDF */
          .react-flow__controls,
          .react-flow__minimap,
          .react-flow__panel {
            display: none !important;
          }

          .react-flow__node {
            pointer-events: none !important;
          }

          /* Ensure proper styling for PDF */
          body {
            margin: 0;
            padding: 0;
            background: white !important;
          }
        `,
        }}
      />
    </div>
  );
}

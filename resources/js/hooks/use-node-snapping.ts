import { AppNode } from '@/store/tree';
import { Node, useReactFlow } from '@xyflow/react';
import { useCallback, useState } from 'react';

export type SnapLine = {
  id: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  orientation: 'horizontal' | 'vertical';
};

export const useNodeSnapping = () => {
  const reactFlow = useReactFlow();
  const [snapLines, setSnapLines] = useState<SnapLine[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const onNodeDrag = useCallback(
    (_: React.MouseEvent, node: AppNode) => {
      if (!isDragging) setIsDragging(true);

      const snapThreshold = 15; // pixels
      const currentNodes = reactFlow.getNodes();
      const otherNodes = currentNodes.filter((n) => n.id !== node.id);

      const newPosition = { ...node.position };
      let snappedX = false;
      let snappedY = false;
      const newSnapLines: SnapLine[] = [];

      // Check for horizontal alignment (same Y position)
      for (const otherNode of otherNodes) {
        if (!snappedY && Math.abs(node.position.y - otherNode.position.y) <= snapThreshold) {
          newPosition.y = otherNode.position.y;
          snappedY = true;

          // Add horizontal snap line
          const minX = Math.min(node.position.x, otherNode.position.x) - 100;
          const maxX =
            Math.max(node.position.x + (node.width || 150), otherNode.position.x + (otherNode.width || 150)) + 100;
          newSnapLines.push({
            id: `h-${otherNode.id}`,
            x1: minX,
            y1: otherNode.position.y,
            x2: maxX,
            y2: otherNode.position.y,
            orientation: 'horizontal',
          });
        }

        // Check for vertical alignment (same X position)
        if (!snappedX && Math.abs(node.position.x - otherNode.position.x) <= snapThreshold) {
          newPosition.x = otherNode.position.x;
          snappedX = true;

          // Add vertical snap line
          const minY = Math.min(node.position.y, otherNode.position.y) - 100;
          const maxY =
            Math.max(node.position.y + (node.height || 100), otherNode.position.y + (otherNode.height || 100)) + 100;
          newSnapLines.push({
            id: `v-${otherNode.id}`,
            x1: otherNode.position.x,
            y1: minY,
            x2: otherNode.position.x,
            y2: maxY,
            orientation: 'vertical',
          });
        }

        // Break early if both axes are snapped
        if (snappedX && snappedY) break;
      }

      // Check for alignment with node centers
      if (!snappedX || !snappedY) {
        for (const otherNode of otherNodes) {
          const otherNodeCenter = {
            x: otherNode.position.x + (otherNode.width || 150) / 2,
            y: otherNode.position.y + (otherNode.height || 100) / 2,
          };

          const currentNodeCenter = {
            x: node.position.x + (node.width || 150) / 2,
            y: node.position.y + (node.height || 100) / 2,
          };

          // Snap to center alignment horizontally
          if (!snappedY && Math.abs(currentNodeCenter.y - otherNodeCenter.y) <= snapThreshold) {
            newPosition.y = otherNode.position.y + (otherNode.height || 100) / 2 - (node.height || 100) / 2;
            snappedY = true;

            // Add horizontal center snap line
            const minX = Math.min(node.position.x, otherNode.position.x) - 100;
            const maxX =
              Math.max(node.position.x + (node.width || 150), otherNode.position.x + (otherNode.width || 150)) + 100;
            newSnapLines.push({
              id: `hc-${otherNode.id}`,
              x1: minX,
              y1: otherNodeCenter.y,
              x2: maxX,
              y2: otherNodeCenter.y,
              orientation: 'horizontal',
            });
          }

          // Snap to center alignment vertically
          if (!snappedX && Math.abs(currentNodeCenter.x - otherNodeCenter.x) <= snapThreshold) {
            newPosition.x = otherNode.position.x + (otherNode.width || 150) / 2 - (node.width || 150) / 2;
            snappedX = true;

            // Add vertical center snap line
            const minY = Math.min(node.position.y, otherNode.position.y) - 100;
            const maxY =
              Math.max(node.position.y + (node.height || 100), otherNode.position.y + (otherNode.height || 100)) + 100;
            newSnapLines.push({
              id: `vc-${otherNode.id}`,
              x1: otherNodeCenter.x,
              y1: minY,
              x2: otherNodeCenter.x,
              y2: maxY,
              orientation: 'vertical',
            });
          }

          // Break early if both axes are snapped
          if (snappedX && snappedY) break;
        }
      }

      // Update snap lines
      setSnapLines(newSnapLines);

      // Update node position if snapping occurred
      if (snappedX || snappedY) {
        reactFlow.setNodes((nodes) => nodes.map((n) => (n.id === node.id ? { ...n, position: newPosition } : n)));
      }
    },
    [reactFlow, isDragging],
  );

  const onNodeDragStop = useCallback((_: Node) => {
    setSnapLines([]);
    setIsDragging(false);
  }, []);

  return {
    snapLines,
    isDragging,
    onNodeDrag,
    onNodeDragStop,
  };
};

import { serve } from 'bun';
import puppeteer from 'puppeteer';

serve({
  port: 3000,
  async fetch(req) {
    let body;
    try {
      body = await req.json();
    } catch {
      return new Response('Invalid JSON', { status: 400 });
    }

    const { url: targetUrl, options = {}, waitForSelector } = body;

    if (!targetUrl || typeof targetUrl !== 'string') {
      return new Response('Missing or invalid "url"', { status: 400 });
    }

    try {
      const browser = await puppeteer.launch({
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.goto(targetUrl, { waitUntil: 'networkidle0' });

      if (waitForSelector) {
        await page.waitForSelector(waitForSelector, { timeout: 15000 });
      }

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        ...options,
      });

      await browser.close();

      return new Response(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': 'inline; filename="page.pdf"',
        },
      });
    } catch (err: any) {
      return new Response(`Error generating PDF: ${err.message}`, {
        status: 500,
      });
    }
  },
});

import { BaseNode } from '@/components/base-node';
import ColorPicker from '@/components/ColorPicker';
import {
  NodeHeader,
  NodeHeaderActions,
  NodeHeaderIcon,
  NodeHeaderMenuAction,
  NodeHeaderTitle,
} from '@/components/node-header';
import ScaleTransition from '@/components/ScaleTransition';
import { Button } from '@/components/ui/button';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useBloodlineHighlight } from '@/hooks/useBloodlineHighlight';
import { useHistoryActions } from '@/hooks/useHistoryActions';
import { cn } from '@/lib/utils';
import { NodeModalType, useNodeModalStore } from '@/store/node-modal';
import { AppNode, TreeState, useTreeStore } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { Female02Icon, Male02Icon, PlusSignIcon, ViewIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { router } from '@inertiajs/react';
import { Handle, NodeProps, Position, useStore } from '@xyflow/react';
import { memo, useCallback } from 'react';
import fastMemo from 'react-fast-memo';

export const nodeTypes = {
  baseNode: memo((props: NodeProps<AppNode>) => {
    const nodesSettings = useTreeStore((s) => s.nodesSettings);
    const { handleNodeClick, isNodeHighlighted } = useBloodlineHighlight();

    const showContent = useStore((s) => s.transform[2] >= 0.2 || !nodesSettings.hideNodesOnZoomOut);
    const { appendToHistory } = useHistoryActions();

    const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

    const node = props.data.nodeModel;

    const isTreeHorizontal = props.data.direction === 'LR';

    const isRootNode = props.data?.isRoot;
    const isHighlighted = isNodeHighlighted(props.id);

    const onClickAdd = useCallback(() => setNodeModal(NodeModalType.NODE_INFO, node), [setNodeModal, node]);

    const handleClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();

        // Handle bloodline highlighting
        handleNodeClick(props.id);

        // Handle original onClickNode if provided
        if (nodesSettings.onClickNode) {
          nodesSettings.onClickNode(node);
        }
      },
      [handleNodeClick, props.id, nodesSettings, node],
    );

    return (
      <BaseNode
        selected={props.selected}
        isHighlighted={isHighlighted}
        onClick={handleClick}
        className={cn(
          'relative px-2 py-1 pb-0',
          isHighlighted
            ? `shadow-lg outline-[1.5px] ${node.gender === 'male' ? 'outline-blue-600' : 'outline-pink-600'}`
            : '',
          'ring-gray-500/20 hover:ring-2',
          isHighlighted && 'z-10 scale-105 transform shadow-xl ring-4 ring-blue-400',
        )}
        dir="rtl"
        style={{
          borderTop: showContent ? `4px solid ${node.bg_color}` : `50px solid ${node.bg_color}`,
          boxShadow: '0 5px 15px rgba(0,0,0,0.08), 0 2px 5px rgba(0,0,0,0.05)',
        }}
      >
        {/* Glow effect */}
        <div
          className="pointer-events-none absolute inset-0 opacity-10"
          style={{
            background: `radial-gradient(ellipse at center, ${node.bg_color} 0%, transparent 100%)`,
          }}
        />
        {showContent ? <NodeContent node={node} editable={nodesSettings.editable} /> : null}

        <Handle
          className="invisible"
          type="source"
          position={isTreeHorizontal ? Position.Right : Position.Bottom}
          id={isTreeHorizontal ? Position.Right : Position.Bottom}
        />

        {props.selected && nodesSettings.editable ? (
          <ScaleTransition>
            <div className="flex flex-col items-center gap-2 px-3 py-2">
              <div>
                <ColorPicker
                  value={node.bg_color}
                  onSelect={(color) => {
                    appendToHistory({ type: 'recolor-node', node: node, payload: color });
                  }}
                />
              </div>
            </div>
          </ScaleTransition>
        ) : null}

        {((isRootNode && props.data.children?.length === 0) || props.selected) &&
          showContent &&
          nodesSettings.editable && (
            <ScaleTransition>
              <div className="flex justify-center">
                <Button size="xs" className="mb-2 w-full" onClick={onClickAdd}>
                  <HugeiconsIcon icon={PlusSignIcon} size={8} />
                  <span className="text-[9px]">اضافة فرد</span>
                </Button>
              </div>
            </ScaleTransition>
          )}

        {/* Target Handle */}
        {!isRootNode && (
          <Handle
            className="invisible"
            type="target"
            position={isTreeHorizontal ? Position.Left : Position.Top}
            id={isTreeHorizontal ? Position.Left : Position.Top}
          />
        )}
      </BaseNode>
    );
  }),
};

type NodeContentProps = TreeState['nodesSettings'] & {
  node: NodeModel;
};

export const NodeContent = fastMemo(({ node, onClickNode, editable }: NodeContentProps) => {
  const removeNode = useTreeStore((s) => s.removeNode);
  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  let textSize = 10;
  if (node.name.length > 7) {
    textSize = 9;
  }

  const onClickView = useCallback(
    () => onClickNode?.(node) ?? setNodeModal(NodeModalType.NODE_INFO, node),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setNodeModal, node],
  );

  const onClickRemove = useCallback(() => {
    if (!confirm('هل أنت متأكد؟')) {
      return;
    }

    router.delete(route('nodes.destroy', node.id), {
      onSuccess: () => removeNode(node.id.toString()),
    });
  }, [node.id, removeNode]);

  return (
    <NodeHeader>
      <NodeHeaderIcon>
        <div className="flex flex-col items-center">
          <div className="grow">
            {node.gender === 'male' ? (
              <HugeiconsIcon size={12} icon={Male02Icon} className="text-blue-500" />
            ) : (
              <HugeiconsIcon size={12} icon={Female02Icon} className="text-pink-500" />
            )}
          </div>
          <div
            className={cn('mt-0.5 rounded border-[0.5px] px-[2px] py-[1px] text-[6px] font-semibold', {
              'border-green-600 text-green-600': node.life_status === 'alive',
              'border-gray-600 text-gray-600': node.life_status === 'dead',
            })}
          >
            {node.life_status === 'alive' ? <span>حي</span> : node.life_status === 'dead' ? <span>متوفى</span> : null}
          </div>
        </div>
      </NodeHeaderIcon>
      <NodeHeaderTitle className="w-full truncate text-nowrap" style={{ fontSize: `${textSize}px` }}>
        {node.name}
      </NodeHeaderTitle>
      <NodeHeaderActions>
        <Button variant="ghost" size="icon" onClick={onClickView} className="-mx-2">
          <div className="flex flex-col justify-center">
            <HugeiconsIcon size={14} icon={ViewIcon} className="text-gray-600" />
            <span className="-mt-0.5 text-[5.5px] font-semibold text-gray-600">عرض</span>
          </div>
        </Button>
        {editable ? (
          <NodeHeaderMenuAction label="">
            <DropdownMenuItem variant="destructive" onClick={onClickRemove}>
              حذف
            </DropdownMenuItem>
          </NodeHeaderMenuAction>
        ) : null}
      </NodeHeaderActions>
    </NodeHeader>
  );
});

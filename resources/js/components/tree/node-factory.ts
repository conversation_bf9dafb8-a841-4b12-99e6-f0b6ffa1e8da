import { nodeHeight, nodeWidth } from '@/components/tree/calculate-layout';
import { AppNode } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { Edge, Position } from '@xyflow/react';

export type NodeFactoryProps = {
  direction?: 'TB' | 'LR';
  isRoot?: boolean;
  children?: number[];
};

export const createReactFlowNode = (
  nodeModel: NodeModel,
  options: NodeFactoryProps & { position: { x: number; y: number } },
): AppNode => {
  const { direction = 'TB', isRoot = false, children = [], position } = options;
  const isTreeHorizontal = direction === 'LR';

  return {
    id: nodeModel.id.toString(),
    data: { nodeModel, direction, isRoot, children },
    type: 'baseNode',
    width: nodeWidth,
    height: nodeHeight,
    position,
    sourcePosition: isTreeHorizontal ? Position.Right : Position.Bottom,
    targetPosition: isTreeHorizontal ? Position.Left : Position.Top,
  };
};

export const createReactFlowEdge = (
  sourceId: string,
  targetId: string,
  options: { direction?: 'TB' | 'LR'; label?: string } = {},
): Edge => {
  const { direction = 'TB', label } = options;
  const isTreeHorizontal = direction === 'LR';

  return {
    id: `e${sourceId}-${targetId}`,
    source: sourceId,
    target: targetId,
    sourceHandle: isTreeHorizontal ? Position.Right : Position.Bottom,
    targetHandle: isTreeHorizontal ? Position.Left : Position.Top,
    label,
    labelShowBg: false,
    labelStyle: {
      textShadow: `
        2px 2px 0 #fff, -2px 2px 0 #fff, 2px -2px 0 #fff, -2px -2px 0 #fff,
        2px 0 0 #fff, -2px 0 0 #fff, 0 2px 0 #fff, 0 -2px 0 #fff
      `,
    },
  };
};

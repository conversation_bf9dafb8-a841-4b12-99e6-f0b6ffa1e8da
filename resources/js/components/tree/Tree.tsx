import AppModal from '@/components/AppModal';
import BallBeatSpinner from '@/components/loaders/BallBeatSpinner';
import NodeFilter from '@/components/NodeFilter';
import { nodeTypes } from '@/components/tree/node-types';
import { SearchTree } from '@/components/tree/search-tree';
import { SnapLines } from '@/components/tree/SnapLines';
import { TreeSettingsPanel } from '@/components/tree/tree-settings-panels';
import Card from '@/components/ui/Card';
import { useNodeSnapping } from '@/hooks/use-node-snapping';
import { useTreeLayout } from '@/hooks/use-tree-layout';
import { useBloodlineHighlight } from '@/hooks/useBloodlineHighlight';
import { useHistoryActions } from '@/hooks/useHistoryActions';
import { AppNode, treeStoreSelector, useTreeStore } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { streamNodes, visitNode } from '@/utils/helpers';
import { HierarchyIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import {
  Background,
  BackgroundVariant,
  Controls,
  FitViewOptions,
  MiniMap,
  Panel,
  ReactFlow,
  useReactFlow,
  Viewport,
} from '@xyflow/react';
import '@xyflow/react/dist/base.css';
import React, { useCallback, useEffect, useState } from 'react';
import { useEvent, useLocalStorage, useMount } from 'react-use';
import { useDebouncedCallback } from 'use-debounce';
import { useShallow } from 'zustand/react/shallow';

export type NodesRequestConfig = {
  url?: string;
  headers?: Record<string, string>;
};

type Props = {
  showSearch?: boolean;
  editable?: boolean;
  onClickSearch?: () => void;
  onClickNode?: (node: NodeModel) => void;
  initialNode?: NodeModel;
  nodesRequestConfig?: NodesRequestConfig;
  hideNodesOnZoomOut?: boolean;
};
export const VIEWPORT_STORAGE_KEY = 'family-tree-viewport';

export const fitViewOptions: FitViewOptions = {
  maxZoom: 1,
  minZoom: 0.5,
  duration: 800,
};

const propOptions = {
  hideAttribution: true,
};

export const Tree = ({
  showSearch = true,
  editable = false,
  onClickSearch,
  initialNode,
  nodesRequestConfig,
  onClickNode,
  hideNodesOnZoomOut = true,
}: Props) => {
  const [status, setStatus] = useState<'fetching' | 'calculating' | 'ready'>('fetching');
  const [savedViewport, setSavedViewport] = useLocalStorage<Viewport | null>(VIEWPORT_STORAGE_KEY, null);

  const reactFlow = useReactFlow();

  const { settings, updateLayout, applyAutoLayout } = useTreeLayout();
  const { clearHighlights, isEdgeHighlighted } = useBloodlineHighlight();

  const [showSearchModal, setShowSearchModal] = useState(false);
  const { appendToHistory } = useHistoryActions();

  const { snapLines, isDragging, onNodeDrag, onNodeDragStop: handleNodeDragStop } = useNodeSnapping();

  const { nodes, edges, onNodesChange, onEdgesChange, setNodesSettings, nodesSettings } = useTreeStore(
    useShallow(treeStoreSelector),
  );

  const debouncedViewportChange = useDebouncedCallback((viewport) => setSavedViewport(viewport), 1000);

  const styledEdges = edges.map((edge) => ({
    ...edge,
    style: {
      ...edge.style,
      stroke: isEdgeHighlighted(edge.id) ? 'gray' : undefined,
      strokeWidth: isEdgeHighlighted(edge.id) ? 2 : undefined,
    },
    animated: isEdgeHighlighted(edge.id),
  }));

  const onNavigateToNode = useCallback(
    (event: CustomEvent) => {
      reactFlow.fitView({ duration: 800, maxZoom: 1, nodes: [{ id: event.detail.toString() }] });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  useEvent('navigateToNode', onNavigateToNode);

  useEffect(() => {
    setNodesSettings({ onClickNode, editable, hideNodesOnZoomOut });
  }, [setNodesSettings, onClickNode, editable, hideNodesOnZoomOut]);

  useMount(async () => {
    const nodes = await streamNodes(nodesRequestConfig);

    setStatus('calculating');

    updateLayout(nodes);

    setStatus('ready');
  });

  const onNodeDragStop = useCallback(
    (_event: React.MouseEvent, node: AppNode) => {
      handleNodeDragStop(node);
      appendToHistory({
        type: 'drag-node',
        node: node.data.nodeModel,
        payload: { x: node.position.x, y: node.position.y },
      });
    },
    [appendToHistory, handleNodeDragStop],
  );

  if (status !== 'ready') {
    return (
      <Card className="relative h-[calc(100svh-16rem)]" containerClassName="p-0">
        <div className="flex h-full items-center justify-center gap-x-2">
          <BallBeatSpinner />
          <span className="font-medium">
            {status === 'fetching' && 'جاري جلب البيانات'}
            {status === 'calculating' && 'جاري الترتيب'}
          </span>
        </div>
      </Card>
    );
  }

  if (!nodes.length) {
    return null;
  }

  return (
    <>
      <ReactFlow
        colorMode="light"
        proOptions={propOptions}
        nodes={nodes}
        edges={styledEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        selectNodesOnDrag={false}
        onlyRenderVisibleElements
        onNodeDrag={onNodeDrag}
        onNodeDragStop={onNodeDragStop}
        onInit={() => {
          setTimeout(() => {
            const hiddenElement = document.createElement('div');
            hiddenElement.id = 'tree-finished';
            document.body.appendChild(hiddenElement);
          }, 1000);

          if (!hideNodesOnZoomOut) {
            reactFlow.fitView({
              maxZoom: 1,
              nodes: nodes.map((n) => ({ id: n.id.toString() })),
              duration: 0,
            });
            return;
          }

          if (initialNode) {
            reactFlow.fitView({ ...fitViewOptions, nodes: [{ id: initialNode.id.toString() }] });

            return;
          }

          if (savedViewport) {
            reactFlow.setViewport(savedViewport);

            return;
          }
        }}
        onViewportChange={debouncedViewportChange}
        onPaneClick={clearHighlights}
        minZoom={!hideNodesOnZoomOut ? 0 : 0.05}
        nodeTypes={nodeTypes}
        nodesDraggable={settings.layout_mode === 'manual' && nodesSettings.editable}
        snapToGrid={settings.layout_mode === 'manual' && nodesSettings.editable}
        snapGrid={[10, 10]}
      >
        {isDragging && <SnapLines snapLines={snapLines} />}
        <MiniMap<AppNode>
          nodeColor={(n) => n.data.nodeModel.bg_color ?? 'gray'}
          nodeStrokeWidth={3}
          zoomable
          pannable
          draggable
          className="!-right-16 !-bottom-13 scale-50 overflow-hidden rounded-lg border md:!-right-3 md:!-bottom-3 md:scale-100"
        />
        <Controls className="text-foreground bg-sidebar !m-1.5 flex cursor-pointer items-center rounded-lg border px-2 py-1.5" />
        <Background patternClassName="stroke-gray-50" variant={BackgroundVariant.Dots} />
        {showSearch ? <SearchTree onClickSearch={() => onClickSearch ?? setShowSearchModal(true)} /> : null}
        {nodesSettings.editable ? (
          <>
            <TreeSettingsPanel />

            {settings.layout_mode === 'manual' && (
              <Panel
                position="top-left"
                className="!top-10 !m-1 flex cursor-pointer items-center gap-1 rounded-lg border border-green-500 bg-green-50 px-2 py-1.5 text-green-800 hover:bg-green-200"
                onClick={applyAutoLayout}
              >
                <span className="text-sm font-medium">تطبيق الترتيب التلقائي</span>
                <HugeiconsIcon size={18} icon={HierarchyIcon} />
              </Panel>
            )}
          </>
        ) : null}
      </ReactFlow>
      <AppModal open={showSearchModal} onOpenChange={setShowSearchModal}>
        <NodeFilter
          resetAfterSelect
          onSelect={(node) => {
            visitNode(node!.id);
            setShowSearchModal(false);
          }}
        />
      </AppModal>
    </>
  );
};

import { forwardRef, HTMLAttributes } from 'react';

import { cn } from '@/lib/utils';

export const BaseNode = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement> & { selected?: boolean; isHighlighted?: boolean }
>(({ className, selected, isHighlighted, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'bg-card text-card-foreground relative w-36 rounded-md transition-all duration-300',
      'outline-border outline-1 hover:outline-gray-500/50',
      className,
      selected ? 'shadow-lg outline-2 outline-gray-300' : '',
      'ring-gray-500/20 hover:ring-2',
      isHighlighted && 'ring-4 ring-blue-400 shadow-xl transform scale-105 z-10',
    )}
    tabIndex={0}
    {...props}
  >
    {props.children}
  </div>
));

BaseNode.displayName = 'BaseNode';
